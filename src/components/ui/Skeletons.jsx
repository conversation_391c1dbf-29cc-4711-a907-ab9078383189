export const PreQualifyExplainerSkeleton = () => {
  return (
    <div className="w-full p-6">
      <div className="mb-6 sm:mb-8">
        <div className="h-12  bg-gray-200 rounded animate-pulse mb-3 sm:mb-4 w-3/4"></div>
      </div>

      <div className="mb-10">
        <div className="h-10 sm:h-8 bg-gray-200 rounded animate-pulse mb-3 sm:mb-4 w-1/2"></div>
        <ul className="space-y-3 sm:space-y-4">
          {[1, 2, 3].map((item) => (
            <li key={item} className="flex items-start">
              <div className="mr-2 mt-0.5">
                <div className="h-6 w-6 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
              <div className="flex-1">
                <div className="h-4 sm:h-6 bg-gray-200 rounded animate-pulse mb-2 w-3/4"></div>
                <div className="h-3 sm:h-4 bg-gray-200 rounded animate-pulse w-full"></div>
              </div>
            </li>
          ))}
        </ul>
      </div>
      <div className="bg-gray-200 h-27 p-2 rounded-sm shadow-sm w-fit animate-pulse">
        <div className="w-44 h-20 bg-gray-300 rounded"></div>
      </div>
    </div>
  );
};

export const QuickLinksSectionSkeleton = () => {
  return (
    <div className="mt-12">
      <div className="h-10 bg-gray-200 rounded animate-pulse mb-6"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {[1, 2, 3].map((item) => (
          <div
            key={item}
            className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm animate-pulse"
          >
            <div className="h-10 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="h-16 bg-gray-200 rounded animate-pulse mb-4"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const FooterSkeleton = () => {
  return (
    <footer className="bg-[#23448F] text-[#B4C2F8] py-4 h-[384px]"></footer>
  );
};
