import { useState, useCallback } from "react";
import { API_ENDPOINT } from "../utils/consts";
import { logger } from "../utils/logger";

const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

const AFFILIATE_CACHE_KEY = "affiliateCache";

function getCachedAffiliate(utmAf) {
  if (typeof window === "undefined") return null;

  try {
    const cache = sessionStorage.getItem(AFFILIATE_CACHE_KEY);
    if (!cache) return null;

    const affiliateCache = JSON.parse(cache);
    return affiliateCache[utmAf] || null;
  } catch {
    return null;
  }
}

function setCachedAffiliate(utmAf, data) {
  if (typeof window === "undefined") return;

  try {
    const cache = sessionStorage.getItem(AFFILIATE_CACHE_KEY);
    const affiliateCache = cache ? JSON.parse(cache) : {};

    affiliateCache[utmAf] = data;
    sessionStorage.setItem(AFFILIATE_CACHE_KEY, JSON.stringify(affiliateCache));
  } catch {
    // Ignore cache errors
  }
}

export function useValidateAffiliateApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  const validateAffiliate = useCallback(async (utmAf) => {
    let error_message = "";

    if (!utmAf) {
      error_message = "No affiliate parameter provided";
      setError({
        message: error_message,
        id: "MISSING_AFFILIATE_PARAM",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    const cachedData = getCachedAffiliate(utmAf);
    if (cachedData) {
      setResult(cachedData);
      setStatus(STATUS.SUCCESS);
      return cachedData;
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      const response = await fetch(
        `${API_ENDPOINT}/affiliates/${utmAf}/validate`
      );
      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        setCachedAffiliate(utmAf, result);

        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      return result;
    } catch (error) {
      logger.error("Failed to validate affiliate parameter:", error);
      setResult({ isValid: false });
      setStatus(STATUS.SUCCESS);
      throw error;
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    validateAffiliate,
    reset,
  };
}
