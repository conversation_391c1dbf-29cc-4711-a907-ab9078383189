import { lazy, useEffect, Suspense } from "react";
import { useNavigate } from "react-router-dom";
import { PreQualificationForm } from "../components/PreQualificationForm/PreQualificationForm";
import { useAppStorage } from "../hooks/useAppStorage";
import {
  isOnFastTrackDomain,
  redirectToFastTrack,
  redirectToMainPortal,
} from "../utils/domainUtils";
import { IS_DEV_MODE } from "../utils/consts.js";
import {
  PreQualifyExplainerSkeleton,
  QuickLinksSectionSkeleton,
} from "../components/ui/Skeletons";

const PreQualifyExplainer = lazy(() =>
  import("../components/ui/PreQualifyExplainer")
);

const QuickLinksSection = lazy(() =>
  import("../components/ui/QuickLinksSection")
);

export const PreQualifyPage = () => {
  const navigate = useNavigate();
  const { fastTrackActive } = useAppStorage();

  useEffect(() => {
    if (fastTrackActive) {
      if (IS_DEV_MODE) {
        navigate("/ft");
        return;
      }

      if (!isOnFastTrackDomain()) {
        redirectToFastTrack();
      } else {
        navigate("/ft");
      }
    } else {
      if (isOnFastTrackDomain()) {
        redirectToMainPortal();
      }
    }
  }, [fastTrackActive, navigate]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col lg:flex-row lg:space-x-8 py-4 lg:py-8">
        <div className="w-full lg:w-1/3 mb-6 lg:mb-0">
          <Suspense fallback={<PreQualifyExplainerSkeleton />}>
            <PreQualifyExplainer />
          </Suspense>
        </div>
        <div className="w-full lg:w-2/3">
          <PreQualificationForm />
        </div>
      </div>
      <Suspense fallback={<QuickLinksSectionSkeleton />}>
        <QuickLinksSection />
      </Suspense>
    </div>
  );
};
